import request from '@/utils/request'

// 查询pdu-out消息列表
export function listOut(query) {
  return request({
    url: '/system/out/list',
    method: 'get',
    params: query
  })
}

// 查询pdu-out消息详细
export function getOut(id) {
  return request({
    url: '/system/out/' + id,
    method: 'get'
  })
}

// 新增pdu-out消息
export function addOut(data) {
  return request({
    url: '/system/out',
    method: 'post',
    data: data
  })
}

// 修改pdu-out消息
export function updateOut(data) {
  return request({
    url: '/system/out',
    method: 'put',
    data: data
  })
}

// 删除pdu-out消息
export function delOut(id) {
  return request({
    url: '/system/out/' + id,
    method: 'delete'
  })
}

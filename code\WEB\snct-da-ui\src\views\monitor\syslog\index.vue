<template>
  <div class="app-container">
      
      <el-button
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="getInfo()"
            style="float: right;"
          >刷新</el-button>
      <div id="logid" style="width:96%;height:780px;background-color: #000000;color:#ffffff;overflow: auto;">
          
      </div>
  </div>
</template>

<script>
import { info, clean } from "@/api/monitor/sysLog";

export default {
  name: "sysLog",
  data() {
    return {

    };
  },
  mounted() {
    this.getInfo();
  },
  methods: {
    getInfo() {
      //获取版本号
      info().then(res => {
        if(res.data.info.length>6){
            document.getElementById("logid").innerHTML=res.data.info;
        }
      });
    }
  }
};
</script>


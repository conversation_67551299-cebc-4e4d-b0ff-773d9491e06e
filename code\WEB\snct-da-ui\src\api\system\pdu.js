import request from '@/utils/request'

// 查询pdu消息列表
export function listPdu(query) {
  return request({
    url: '/system/pdu/list',
    method: 'get',
    params: query
  })
}

// 查询pdu消息详细
export function getPdu(id) {
  return request({
    url: '/system/pdu/' + id,
    method: 'get'
  })
}

// 新增pdu消息
export function addPdu(data) {
  return request({
    url: '/system/pdu',
    method: 'post',
    data: data
  })
}

// 修改pdu消息
export function updatePdu(data) {
  return request({
    url: '/system/pdu',
    method: 'put',
    data: data
  })
}

// 删除pdu消息
export function delPdu(id) {
  return request({
    url: '/system/pdu/' + id,
    method: 'delete'
  })
}

<template>
  <div class="app-container home">
    <el-row :gutter="20">
      <el-col :sm="24" :lg="12" style="padding-left: 20px">
        <h2>SNCT船舶数据采集</h2>
        <p>
          ----------------------------------------------------------------
        </p>
        <p style="font-size:14px; color: #909399; margin-top: 15px;">
          <b>当前版本:</b> <span>{{ currentVersion || '获取中...' }}</span>
        </p>
      </el-col>

      <el-col :sm="24" :lg="12" style="padding-right: 20px; text-align: right;">
        <div class="time-section">
          <div class="time-display">
            <span class="time-label">系统时间：</span>
            <span class="time-value">{{ currentTime }}</span>
            <el-button
              type="text"
              size="medium"
              @click="refreshTime"
              :loading="refreshing"
              style="margin-left: 8px;"
            >
              <i class="el-icon-refresh"></i>
            </el-button>
          </div>
          <div class="time-actions" style="margin-top: 8px;">
            <el-button
              size="medium"
              type="text"
              @click="showTimeDialog = true"
            >
              修改时间
            </el-button>
          </div>
        </div>
      </el-col>

    </el-row>
    <el-divider />

    <!-- 时间修改对话框 -->
    <el-dialog
      title="修改系统时间"
      :visible.sync="showTimeDialog"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form :model="timeForm" :rules="timeRules" ref="timeForm" label-width="80px">
        <el-form-item label="系统时间" prop="dateTime">
          <el-date-picker
            v-model="timeForm.dateTime"
            type="datetime"
            placeholder="选择日期时间"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 100%;"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showTimeDialog = false">取 消</el-button>
        <el-button type="primary" @click="updateSystemTime" :loading="updating">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getCurrentTime, updateTime } from "@/api/system/datetime/datetime";
import { getVersion } from "@/api/system/upload/upload";

export default {
  name: "Index",
  data() {
    return {
      // 版本号
      version: "1.0.0",
      // 当前系统版本
      currentVersion: "",
      // 当前时间
      currentTime: "",
      // 系统时间基准
      systemTimeBase: null,
      // 本地时间基准
      localTimeBase: null,
      // 时间刷新状态
      refreshing: false,
      // 时间更新状态
      updating: false,
      // 显示时间修改对话框
      showTimeDialog: false,
      // 时间表单
      timeForm: {
        dateTime: ""
      },
      // 表单验证规则
      timeRules: {
        dateTime: [
          { required: true, message: "请选择时间", trigger: "change" }
        ]
      },
      // 定时器
      timeTimer: null
    };
  },
  mounted() {
    this.loadCurrentTime();
    this.loadVersion();
    // 每秒更新显示时间
    this.timeTimer = setInterval(() => {
      this.updateDisplayTime();
    }, 1000);
  },
  beforeDestroy() {
    if (this.timeTimer) {
      clearInterval(this.timeTimer);
    }
  },
  methods: {
    goTarget(href) {
      window.open(href, "_blank");
    },
    // 加载版本号
    async loadVersion() {
      try {
        const response = await getVersion();
        if (response && response.version) {
          this.currentVersion = response.version;
        } else {
          this.currentVersion = "未知版本";
        }
      } catch (error) {
        console.error("获取版本号失败:", error);
        this.currentVersion = "获取失败";
      }
    },
    // 加载当前时间
    async loadCurrentTime() {
      try {
        const response = await getCurrentTime();
        if (response.code === 200) {
          const systemTimeStr = response.data.systemTime || response.data.javaTime;
          if (systemTimeStr && systemTimeStr !== "获取失败") {
            // 记录系统时间基准和本地时间基准
            this.systemTimeBase = new Date(systemTimeStr.replace(/-/g, '/'));
            this.localTimeBase = new Date();
            this.updateDisplayTime();
          } else {
            this.currentTime = "获取失败";
          }
        }
      } catch (error) {
        console.error("获取系统时间失败:", error);
        this.currentTime = "获取失败";
      }
    },
    // 更新显示时间（基于本地计算）
    updateDisplayTime() {
      if (this.systemTimeBase && this.localTimeBase) {
        const now = new Date();
        const elapsed = now.getTime() - this.localTimeBase.getTime();
        const currentSystemTime = new Date(this.systemTimeBase.getTime() + elapsed);
        this.currentTime = this.formatDateTime(currentSystemTime);
      }
    },
    // 格式化时间显示
    formatDateTime(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    // 刷新时间（重新获取系统时间基准）
    async refreshTime() {
      this.refreshing = true;
      try {
        await this.loadCurrentTime();
        this.$message.success("时间刷新成功");
      } catch (error) {
        this.$message.error("时间刷新失败");
      } finally {
        this.refreshing = false;
      }
    },
    // 更新系统时间
    updateSystemTime() {
      this.$refs.timeForm.validate(async (valid) => {
        if (valid) {
          this.updating = true;
          try {
            const response = await updateTime(this.timeForm.dateTime);
            if (response.code === 200) {
              this.$message.success("系统时间更新成功");
              this.showTimeDialog = false;
              // 更新时间基准为新设置的时间
              this.systemTimeBase = new Date(this.timeForm.dateTime.replace(/-/g, '/'));
              this.localTimeBase = new Date();
              this.updateDisplayTime();
            } else {
              this.$message.error(response.msg || "系统时间更新失败");
            }
          } catch (error) {
            this.$message.error("系统时间更新失败");
          } finally {
            this.updating = false;
          }
        }
      });
    }
  }
};
</script>

<style scoped lang="scss">
.home {
  blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #eee;
  }
  hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee;
  }
  .col-item {
    margin-bottom: 20px;
  }

  ul {
    padding: 0;
    margin: 0;
  }

  font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 13px;
  color: #676a6c;
  overflow-x: hidden;

  ul {
    list-style-type: none;
  }

  h4 {
    margin-top: 0px;
  }

  h2 {
    margin-top: 10px;
    font-size: 26px;
    font-weight: 100;
  }

  p {
    margin-top: 10px;

    b {
      font-weight: 700;
    }
  }

  .update-log {
    ol {
      display: block;
      list-style-type: decimal;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0;
      margin-inline-end: 0;
      padding-inline-start: 40px;
    }
  }

  .time-section {
    margin-top: 20px;

    .time-display {
      font-size: 18px;

      .time-label {
        color: #606266;
        margin-right: 8px;
      }

      .time-value {
        font-family: 'Courier New', monospace;
        color: #303133;
      }
    }

    .time-actions {
      text-align: right;
    }
  }
}
</style>


import request from '@/utils/request'

// 查询Modem消息列表
export function listModem(query) {
  return request({
    url: '/system/modem/list',
    method: 'get',
    params: query
  })
}

// 查询Modem消息详细
export function getModem(id) {
  return request({
    url: '/system/modem/' + id,
    method: 'get'
  })
}

// 新增Modem消息
export function addModem(data) {
  return request({
    url: '/system/modem',
    method: 'post',
    data: data
  })
}

// 修改Modem消息
export function updateModem(data) {
  return request({
    url: '/system/modem',
    method: 'put',
    data: data
  })
}

// 删除Modem消息
export function delModem(id) {
  return request({
    url: '/system/modem/' + id,
    method: 'delete'
  })
}

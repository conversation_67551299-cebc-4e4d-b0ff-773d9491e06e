import request from '@/utils/request'

// 查询数据权限列表
export function listrestart(query) {
  return request({
    url: '/system/restart/list',
    method: 'get',
    params: query
  })
}

// 查询数据权限详细
export function getrestart(restartId) {
  return request({
    url: '/system/restart/' + restartId,
    method: 'get'
  })
}

// 新增数据权限
export function addrestart(data) {
  return request({
    url: '/system/restart',
    method: 'post',
    data: data
  })
}

// 修改数据权限
export function updaterestart(data) {
  return request({
    url: '/system/restart',
    method: 'put',
    data: data
  })
}

// 删除数据权限
export function delrestart(restartId) {
  return request({
    url: '/system/restart/' + restartId,
    method: 'delete'
  })
}

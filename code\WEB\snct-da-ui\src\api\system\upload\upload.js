import request from '@/utils/request'

// 版本文件上传
export function uploadVersion(data) {
  return request({
    url: '/system/upload/v',
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    data: data
  })
}

// 系统重启
export function reboot() {
  return request({
    url: '/system/upload/reboot',
    method: 'post'
  })
}

// 重启服务
export function restart() {
  return request({
    url: '/system/upload/restart',
    method: 'post'
  })
}

//获取版本号
export function getVersion() {
  return request({
    url: '/system/upload/getVersion',
    method: 'post'
  })
}

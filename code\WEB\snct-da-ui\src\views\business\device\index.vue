<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="设备名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入设备名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['business:device:add']">新增</el-button>
      </el-col>
      <!--
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-upload"
          size="mini"
          @click="handleSync"
          v-hasPermi="['business:device:edit']"
        >同步</el-button>
      </el-col>
      -->
      <!--
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['business:device:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['business:device:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['business:device:export']">导出</el-button>
      </el-col>
      -->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="deviceList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" width="70" align="center">
        <template slot-scope="scope">
          <span>{{(queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1}}</span>
        </template>
      </el-table-column>
      <el-table-column label="设备名称" align="center" prop="name" />
      <!-- <el-table-column label="设备编号" align="center" prop="code" /> -->
      <!-- <el-table-column label="优先级" align="center" prop="cost" /> -->
      <el-table-column label="连接方式" align="center" prop="connectType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.connect_type" :value="scope.row.connectType" />
        </template>
      </el-table-column>
      <el-table-column label="连接状态" align="center" prop="connectStatus">
        <template slot-scope="scope">
          {{ scope.row.connectStatus === 1 ? "连接" : "中断" }}
        </template>
      </el-table-column>
      <el-table-column label="连接信息" align="center" width="165">
        <template slot-scope="scope">
          <span v-if="scope.row.connectType === 1">
            {{ scope.row.serialPort ? scope.row.serialPort : '-' }},
            {{ scope.row.baudRate ? scope.row.baudRate : '-' }},
            {{ scope.row.dataBits ? scope.row.dataBits : '-' }},
            {{ scope.row.stopBits ? scope.row.stopBits : '-' }},
            {{ (scope.row.parity !== null && scope.row.parity !== undefined) ? scope.row.parity : '-' }}
          </span>
          <span v-else>
            {{ scope.row.ip ? scope.row.ip : '-' }}:{{ scope.row.port ? scope.row.port : '-' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="设备类型" align="center" prop="type">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.device_type" :value="scope.row.type" />
        </template>
      </el-table-column>
      
      <!-- <el-table-column label="传输状态" align="center" prop="transferStatus">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.transferStatus" :active-value="1" :inactive-value="0">
          </el-switch>
        </template>
      </el-table-column> -->
    
      <el-table-column label="启用状态" align="center" prop="enable">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.enable"
            :active-value="1"
            :inactive-value="0"
            @change="handleEnableChange(scope.row)">
          </el-switch>
        </template>
      </el-table-column>
     
      <!-- <el-table-column label="启用状态" align="center" prop="enable">
        <template slot-scope="scope">
          <el-tag :type="scope.row.enable === 1 ? 'success' : 'danger'">
            {{ scope.row.enable === 1 ? "正常" : "未启用" }}
          </el-tag>
        </template>
      </el-table-column> -->

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)"
            v-hasPermi="['business:device:view']">预览</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['business:device:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['business:device:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改设备对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="760px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px" :validate-on-rule-change="false">

        <table>
          <tr>
            <td>
              <el-form-item label="设备名称" prop="name" style="margin-bottom:0px;">
                <el-input v-model="form.name" placeholder="请输入设备名称" style="width:240px"/>
              </el-form-item>
            </td>
            <td>
              <!-- 普通设备的编号输入框 -->
              <el-form-item v-show="form.type !== 32" label="设备编号" prop="code" style="margin-bottom:0px;">
                <el-input v-model="form.code" placeholder="请输入设备编号" style="width:240px"/>
              </el-form-item>
              <!-- 北斗设备的基础编号输入框 -->
              <el-form-item v-show="form.type === 32" label="基础编号" prop="baseCode" style="margin-bottom:0px;">
                <el-input v-model="form.baseCode" placeholder="请输入设备基础编号" style="width:240px"/>
              </el-form-item>
            </td>
          </tr>
          <tr>
            <td>
              <!-- 普通设备显示启用状态 -->
              <el-form-item v-show="form.type !== 32" label="启用状态" prop="enable" style="margin-bottom:0px;">
                <el-switch v-model="form.enable" :active-value="1" :inactive-value="0"></el-switch>
              </el-form-item>
              <!-- 北斗设备显示指挥机卡号 -->
              <el-form-item v-show="form.type === 32" label="指挥机卡号" prop="beidouCardNo" style="margin-bottom:0px;">
                <el-input v-model="form.beidouCardNo" placeholder="请输入指挥机卡号" style="width:240px"/>
              </el-form-item>
            </td>
            <td>
              <el-form-item label="设备类型" prop="type" style="margin-bottom:0px;">
                <el-select v-model="form.type" placeholder="请选择设备类型"
                  :style="form.type === 32 ? 'width:120px; margin-right: 10px;' : 'width:240px'">
                  <el-option v-for="dict in dict.type.device_type" :key="dict.value" :label="dict.label"
                    :value="parseInt(dict.value)" />
                </el-select>
                <!-- 北斗设备显示启用状态开关 -->
                <el-switch v-show="form.type === 32" v-model="form.enable" :active-value="1" :inactive-value="0"
                  active-text="启用" inactive-text="禁用"></el-switch>
              </el-form-item>
            </td>
          </tr>
          <tr>
            <td>
        <el-form-item label="连接方式" prop="connectType" style="margin-bottom:0px;">
          <el-select v-model="form.connectType" placeholder="请选择连接方式" style="width:240px">
            <el-option v-for="dict in dict.type.connect_type" :key="dict.value" :label="dict.label"
              :value="parseInt(dict.value)" />
          </el-select>
        </el-form-item>
            </td>
            <td>
        <el-form-item v-show="form.connectType === 1" label="串口号" prop="serialPort" style="margin-bottom:0px;">
          <el-select v-model="form.serialPort" placeholder="请选择串口号" style="width:240px">
            <el-option v-for="dict in availableSerialPorts" :key="dict.value" :label="dict.label"
              :value="dict.value" :disabled="dict.disabled" />
          </el-select>
        </el-form-item>
            </td>
          </tr>
          <!-- 串口连接配置 - 只在连接类型为1时显示 -->
          <tr v-show="form.connectType === 1">
            <td>
        <el-form-item label="波特率" prop="baudRate" style="margin-bottom:0px;">
          <el-select v-model="form.baudRate" placeholder="请选择波特率" style="width:240px">
            <el-option v-for="dict in dict.type.baud_rate" :key="dict.value" :label="dict.label"
              :value="parseInt(dict.value)" />
          </el-select>
        </el-form-item>
            </td>
            <td>
        <el-form-item label="数据位" prop="dataBits" style="margin-bottom:0px;">
          <el-select v-model="form.dataBits" placeholder="请选择数据位" style="width:240px">
            <el-option v-for="dict in dict.type.data_bits" :key="dict.value" :label="dict.label"
              :value="parseInt(dict.value)" />
          </el-select>
        </el-form-item>
            </td>
          </tr>
          <tr v-show="form.connectType === 1">
            <td>
        <el-form-item label="停止位" prop="stopBits" style="margin-bottom:0px;">
          <el-select v-model="form.stopBits" placeholder="请选择停止位" style="width:240px">
            <el-option v-for="dict in dict.type.stop_bits" :key="dict.value" :label="dict.label"
              :value="parseInt(dict.value)" />
          </el-select>
        </el-form-item>
            </td>
            <td>
        <el-form-item label="校验位" prop="parity" style="margin-bottom:0px;">
          <el-select v-model="form.parity" placeholder="请选择校验位" style="width:240px">
            <el-option v-for="dict in dict.type.parity" :key="dict.value" :label="dict.label"
              :value="parseInt(dict.value)" />
          </el-select>
        </el-form-item>
            </td>
          </tr>

          <!-- 网络连接配置 - 只在连接类型不为1时显示 -->
          <tr v-show="form.connectType !== 1">
            <td>
        <el-form-item label="IP" prop="ip" style="margin-bottom:0px;">
          <el-input v-model="form.ip" placeholder="请输入IP" style="width:240px"/>
        </el-form-item>
            </td>
            <td>
        <el-form-item label="端口号" prop="port" style="margin-bottom:0px;">
          <el-input v-model="form.port" placeholder="请输入端口号" style="width:240px"/>
        </el-form-item>
            </td>
          </tr>
          <tr>
            <td>
        <el-form-item label="传输间隔" prop="compartment" style="margin-bottom:0px;">
          <el-input v-model="form.compartment" placeholder="请输入传输间隔(单位：秒)"  style="width:240px"/>
        </el-form-item>
            </td>
            <td>
        <el-form-item v-show="form.type === 32" label="消息长度" prop="msgLength" style="margin-bottom:0px;">
          <el-input v-model="form.msgLength" placeholder="请输入消息长度(单位：字节)" style="width:240px"/>
        </el-form-item>
        <el-form-item v-show="form.type !== 32" label="备注" prop="remark" style="margin-bottom:0px;">
          <el-input v-model="form.remark" placeholder="请输入备注" style="width:240px"/>
        </el-form-item>
            </td>
          </tr>
          <tr v-show="form.type === 32">
            <td colspan="2">
        <el-form-item label="备注" prop="remark" style="margin-bottom:0px;">
          <el-input v-model="form.remark" placeholder="请输入备注" style="width:240px"/>
        </el-form-item>
            </td>
          </tr>
        </table>

        <!--
        <el-form-item label="优先级" prop="cost">
          <el-input v-model="form.cost" placeholder="请输入优先级" />
        </el-form-item>
        -->
        <!--
        <el-form-item label="机器MAC地址" prop="mac">
          <el-input v-model="form.mac" placeholder="请输入机器MAC地址" />
        </el-form-item>
        -->

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>


    <!-- 预览设备对话框 -->
    <el-dialog :title="title" :visible.sync="view" width="620px" append-to-body  @close='closeDialog' >
      <div :visible.sync="view" id="vid" style="width:614px;height:530px;background-color: #000000;color:#ffffff;overflow: auto;margin-top:-29px;margin-bottom:-27px;margin-left:-17px;margin-right:-17px">
      </div>
    </el-dialog>


  </div>
</template>

<script>
import { listDevice, getDevice, delDevice, addDevice, updateDevice, syncDevice, refData,refreshdataclose, enableDevice, disableDevice } from "@/api/system/device";

export default {
  name: "Device",
  dicts: ["device_type", "connect_type","ship_status", "baud_rate", "data_bits", "stop_bits", "parity", "serial_port"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 设备表格数据
      deviceList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层
      view: false,
      deid:0,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deptId: null,
        sn: null,
        name: null,
        type: null,
        code: null,
        cost: null,
        enable: null,
        mac: null,
        transferStatus: null,
        compartment: null,
        connectType: null,
        baudRate: null,
        dataBits: null,
        stopBits: null,
        parity: null,
        serialPort: null,
        connectStatus: null,
        msgLength: null
      },
      // 表单参数
      form: {},
      // 基础表单校验规则
      baseRules: {
        deptId: [
          { required: true, message: "部门ID不能为空", trigger: "submit" }
        ],
        sn: [
          { required: true, message: "船sn不能为空", trigger: "submit" }
        ],
        name: [
          { required: true, message: "设备名称不能为空", trigger: "submit" }
        ],
        type: [
          { required: true, message: "设备类型不能为空", trigger: "submit" }
        ],
        connectType: [
          { required: true, message: "连接方式不能为空", trigger: "submit" }
        ],
      }
    };
  },
  computed: {
    // 动态表单验证规则
    rules() {
      const rules = { ...this.baseRules };

      if (this.form.type === 32) {
        // 北斗设备需要验证baseCode和beidouCardNo
        rules.baseCode = [
          { required: true, message: "设备基础编号不能为空", trigger: "submit" }
        ];
        rules.beidouCardNo = [
          { required: true, message: "指挥机卡号不能为空", trigger: "submit" }
        ];
      } else {
        // 普通设备需要验证code
        rules.code = [
          { required: true, message: "设备编号不能为空", trigger: "submit" }
        ];
      }

      // 根据连接类型添加相应的验证规则
      if (this.form.connectType === 1) {
        // 串口连接需要验证串口相关字段
        rules.serialPort = [
          { required: true, message: "串口号不能为空", trigger: "submit" }
        ];
        rules.baudRate = [
          { required: true, message: "波特率不能为空", trigger: "submit" }
        ];
        rules.dataBits = [
          { required: true, message: "数据位不能为空", trigger: "submit" }
        ];
        rules.stopBits = [
          { required: true, message: "停止位不能为空", trigger: "submit" }
        ];
        rules.parity = [
          { required: true, message: "校验位不能为空", trigger: "submit" }
        ];
      } else if (this.form.connectType && this.form.connectType !== 1) {
        // 网络连接需要验证IP和端口
        rules.ip = [
          { required: true, message: "IP地址不能为空", trigger: "submit" }
        ];
        rules.port = [
          { required: true, message: "端口号不能为空", trigger: "submit" }
        ];
      }

      return rules;
    },
    // 可用的串口列表（过滤已被使用的串口）
    availableSerialPorts() {
      if (!this.dict.type.serial_port) {
        return [];
      }

      // 获取已被其他设备使用的串口号列表
      const usedSerialPorts = this.deviceList
        .filter(device =>
          device.connectType === 1 && // 串口连接
          device.serialPort && // 有串口号
          device.id !== this.form.id // 排除当前编辑的设备
        )
        .map(device => device.serialPort);

      // 为每个串口选项添加禁用状态
      return this.dict.type.serial_port.map(port => ({
        ...port,
        disabled: usedSerialPorts.includes(port.value),
        label: usedSerialPorts.includes(port.value)
          ? `${port.label} (已被使用)`
          : port.label
      }));
    }
  },
  created() {
    this.getList();
  },
  watch: {
    // 监听设备类型变化
    'form.type'(newType, oldType) {
      // 当从北斗设备切换到其他设备类型时，清空北斗相关字段
      if (oldType === 32 && newType !== 32) {
        this.form.baseCode = null;
        this.form.beidouCardNo = null;
        this.form.msgLength = null;
      }
      // 当从其他设备类型切换到北斗设备时，清空原编号字段
      if (oldType !== 32 && newType === 32) {
        this.form.code = null;
      }

      // 清除表单验证状态
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
        }
      });
    },
    // 监听连接类型变化
    'form.connectType'(newConnectType, oldConnectType) {
      // 当切换连接类型时，清空相关字段
      if (newConnectType === 1) {
        // 切换到串口连接，清空网络连接相关字段
        this.form.ip = null;
        this.form.port = null;
      } else {
        // 切换到网络连接，清空串口连接相关字段
        this.form.baudRate = null;
        this.form.dataBits = null;
        this.form.stopBits = null;
        this.form.parity = null;
        this.form.serialPort = null;
      }

      // 清除表单验证状态
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
        }
      });
    },
    // 监听串口号变化，检查是否被占用
    'form.serialPort'(newSerialPort) {
      if (newSerialPort && this.form.connectType === 1) {
        // 检查串口号是否被其他设备使用
        const isUsed = this.deviceList.some(device =>
          device.connectType === 1 &&
          device.serialPort === newSerialPort &&
          device.id !== this.form.id
        );

        if (isUsed) {
          this.$message.warning(`串口号 ${newSerialPort} 已被其他设备使用，请选择其他串口号`);
          // 清空当前选择
          this.$nextTick(() => {
            this.form.serialPort = null;
          });
        }
      }
    }
  },
  methods: {

    /** 查询设备列表 */
    getList() {
      this.loading = true;
      listDevice(this.queryParams).then(response => {
        this.deviceList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        deptId: null,
        sn: null,
        name: null,
        type: null,
        code: null,
        baseCode: null,        // 北斗设备基础编号
        beidouCardNo: null,    // 指挥机卡号
        cost: null,
        enable: null,
        mac: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null,
        transferStatus: null,
        compartment: null,
        connectType: null,
        baudRate: null,
        dataBits: null,
        stopBits: null,
        parity: null,
        serialPort: null,
        connectStatus: null,
        msgLength: null         // 消息长度限制
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },

    // 拆分北斗设备编号
    splitBeidouCode(code) {
      if (!code || typeof code !== 'string') {
        return { baseCode: '', beidouCardNo: '' };
      }
      const lastDashIndex = code.lastIndexOf('-');
      if (lastDashIndex === -1) {
        return { baseCode: code, beidouCardNo: '' };
      }
      return {
        baseCode: code.substring(0, lastDashIndex),
        beidouCardNo: code.substring(lastDashIndex + 1)
      };
    },

    // 拼接北斗设备编号
    combineBeidouCode(baseCode, beidouCardNo) {
      if (!baseCode && !beidouCardNo) {
        return '';
      }
      if (!baseCode) {
        return beidouCardNo;
      }
      if (!beidouCardNo) {
        return baseCode;
      }
      return `${baseCode}-${beidouCardNo}`;
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加设备";

      // 清除表单验证状态
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.open = true;
      this.title = "修改设备";

      const id = row.id || this.ids
      getDevice(id).then(response => {
        this.form = response.data;

        // 如果是北斗设备（类型32），拆分编号
        if (this.form.type === 32 && this.form.code) {
          const { baseCode, beidouCardNo } = this.splitBeidouCode(this.form.code);
          this.$set(this.form, 'baseCode', baseCode);
          this.$set(this.form, 'beidouCardNo', beidouCardNo);
        }

        // 确保表单重新渲染并清除验证
        this.$nextTick(() => {
          this.$refs.form.clearValidate();
        });
      });
    },
    /** 预览按钮操作 */
    handleView(row) {
      this.reset();
      this.deid = row.id;
      //const id = row.id || this.ids
      this.view = true;
      this.title = row.name+"  —  实时数据预览";
      this.timer = setInterval(()=>{
        this.refreshdata(row.id,row.code);
      },2000)
    },
    refreshdata(deviceid,code){

      var objv = document.getElementById("vid");
      if(objv!=null){
        refData(deviceid,code).then(response => {
            //const data = response.data;
            console.log('##########55##' + response.msg)
            if(response.msg.length>5){
              var m = response.msg;
              var ms = m.split('###');
              if(this.deid==ms[1]&&ms[0].length>5){
                  var innerHTML = objv.innerHTML
                  objv.innerHTML =   ms[0] + innerHTML ;
              }
            }
          });
      }
    },
    closeDialog(){
      var objv = document.getElementById("vid");
      objv.innerHTML = "";
      clearInterval(this.timer);
      this.deid=0;
      //this.clearTimer();
      refreshdataclose().then(response => {
        console.log('##########refreshdataclose##########' + response.msg)
      });
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 创建提交数据的副本
          const submitData = { ...this.form };

          // 如果是北斗设备（类型32），拼接编号
          if (submitData.type === 32) {
            submitData.code = this.combineBeidouCode(submitData.baseCode, submitData.beidouCardNo);
            // 删除临时字段，避免传递到后端
            delete submitData.baseCode;
            delete submitData.beidouCardNo;
          }

          if (this.form.id != null) {
            updateDevice(submitData).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDevice(submitData).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除设备编号为"' + ids + '"的数据项？').then(function () {
        return delDevice(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('business/device/export', {
        ...this.queryParams
      }, `device_${new Date().getTime()}.xlsx`)
    },
    /** 同步设备信息 */
    handleSync() {
      this.$modal.confirm('确认同步设备信息吗？').then(() => {
        this.loading = true;
        syncDevice().then(response => {
          this.$modal.msgSuccess(response.msg);
          this.loading = false;
        }).catch(() => {
          this.loading = false;
        });
      }).catch(() => {});
    },
    /** 处理启用状态开关变化 */
    handleEnableChange(row) {
      const action = row.enable === 1 ? '启用' : '禁用';
      this.$modal.confirm(`确认${action}设备"${row.name}"吗？`).then(() => {
        const apiCall = row.enable === 1 ? enableDevice(row.id) : disableDevice(row.id);
        apiCall.then(response => {
          this.$modal.msgSuccess(response.msg);
          this.getList(); // 刷新列表
        }).catch(error => {
          // 如果操作失败，恢复开关状态
          row.enable = row.enable === 1 ? 0 : 1;
          this.$modal.msgError(error.msg || `${action}设备失败`);
        });
      }).catch(() => {
        // 用户取消操作，恢复开关状态
        row.enable = row.enable === 1 ? 0 : 1;
      });
    },
  }
};
</script>

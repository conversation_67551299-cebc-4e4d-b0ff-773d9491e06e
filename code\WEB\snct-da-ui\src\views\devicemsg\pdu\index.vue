<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="部门ID" prop="deptId">
        <el-input
          v-model="queryParams.deptId"
          placeholder="请输入部门ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="船只id" prop="shipId">
        <el-input
          v-model="queryParams.shipId"
          placeholder="请输入船只id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="总电能" prop="manage">
        <el-input
          v-model="queryParams.manage"
          placeholder="请输入总电能"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="电流" prop="electric">
        <el-input
          v-model="queryParams.electric"
          placeholder="请输入电流"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="电压" prop="voltage">
        <el-input
          v-model="queryParams.voltage"
          placeholder="请输入电压"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="有功功率" prop="yesPwoer">
        <el-input
          v-model="queryParams.yesPwoer"
          placeholder="请输入有功功率"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="无功功率" prop="noPwoer">
        <el-input
          v-model="queryParams.noPwoer"
          placeholder="请输入无功功率"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="视在功率" prop="seePwoer">
        <el-input
          v-model="queryParams.seePwoer"
          placeholder="请输入视在功率"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="功率因数" prop="powerParam">
        <el-input
          v-model="queryParams.powerParam"
          placeholder="请输入功率因数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:pdu:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:pdu:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:pdu:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:pdu:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="pduList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="部门ID" align="center" prop="deptId" />
      <el-table-column label="船只id" align="center" prop="shipId" />
      <el-table-column label="总电能" align="center" prop="manage" />
      <el-table-column label="电流" align="center" prop="electric" />
      <el-table-column label="电压" align="center" prop="voltage" />
      <el-table-column label="有功功率" align="center" prop="yesPwoer" />
      <el-table-column label="无功功率" align="center" prop="noPwoer" />
      <el-table-column label="视在功率" align="center" prop="seePwoer" />
      <el-table-column label="功率因数" align="center" prop="powerParam" />
      <el-table-column label="状态 0默认 1发送云端成功 2发送云端失败" align="center" prop="status" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:pdu:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:pdu:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改pdu消息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="部门ID" prop="deptId">
          <el-input v-model="form.deptId" placeholder="请输入部门ID" />
        </el-form-item>
        <el-form-item label="船只id" prop="shipId">
          <el-input v-model="form.shipId" placeholder="请输入船只id" />
        </el-form-item>
        <el-form-item label="总电能" prop="manage">
          <el-input v-model="form.manage" placeholder="请输入总电能" />
        </el-form-item>
        <el-form-item label="电流" prop="electric">
          <el-input v-model="form.electric" placeholder="请输入电流" />
        </el-form-item>
        <el-form-item label="电压" prop="voltage">
          <el-input v-model="form.voltage" placeholder="请输入电压" />
        </el-form-item>
        <el-form-item label="有功功率" prop="yesPwoer">
          <el-input v-model="form.yesPwoer" placeholder="请输入有功功率" />
        </el-form-item>
        <el-form-item label="无功功率" prop="noPwoer">
          <el-input v-model="form.noPwoer" placeholder="请输入无功功率" />
        </el-form-item>
        <el-form-item label="视在功率" prop="seePwoer">
          <el-input v-model="form.seePwoer" placeholder="请输入视在功率" />
        </el-form-item>
        <el-form-item label="功率因数" prop="powerParam">
          <el-input v-model="form.powerParam" placeholder="请输入功率因数" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPdu, getPdu, delPdu, addPdu, updatePdu } from "@/api/system/pdu";

export default {
  name: "Pdu",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // pdu消息表格数据
      pduList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deptId: null,
        shipId: null,
        manage: null,
        electric: null,
        voltage: null,
        yesPwoer: null,
        noPwoer: null,
        seePwoer: null,
        powerParam: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        deptId: [
          { required: true, message: "部门ID不能为空", trigger: "blur" }
        ],
        shipId: [
          { required: true, message: "船只id不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询pdu消息列表 */
    getList() {
      this.loading = true;
      listPdu(this.queryParams).then(response => {
        this.pduList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        deptId: null,
        shipId: null,
        manage: null,
        electric: null,
        voltage: null,
        yesPwoer: null,
        noPwoer: null,
        seePwoer: null,
        powerParam: null,
        status: null,
        createTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加pdu消息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getPdu(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改pdu消息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updatePdu(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPdu(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除pdu消息编号为"' + ids + '"的数据项？').then(function() {
        return delPdu(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/pdu/export', {
        ...this.queryParams
      }, `pdu_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

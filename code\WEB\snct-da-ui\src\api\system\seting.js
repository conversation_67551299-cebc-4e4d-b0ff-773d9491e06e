import request from '@/utils/request'

// 查询参数列表
export function setingInfo() {
  return request({
    url: '/system/seting/info',
    method: 'get',
  })
}

// 修改参数配置
export function updatesetingInfo(data) {
  return request({
    url: '/system/seting',
    method: 'put',
    data: data
  })
}


// 重启服务
export function restart() {
  return request({
    url: '/system/upload/restart',
    method: 'post'
  })
}

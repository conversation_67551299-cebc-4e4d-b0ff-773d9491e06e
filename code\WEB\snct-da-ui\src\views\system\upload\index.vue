<template>
  <div class="app-container">
    <!-- 版本更新管理 -->
    <el-card class="update-card" shadow="hover">
      <div slot="header" class="card-header">
        <span class="card-title">
          <i class="el-icon-upload2"></i>
          版本更新
        </span>
        <span id="versiontext" style="color:red;"></span>
      </div>

      <div class="update-content">
        <!-- 文件上传组件 -->
        <div class="upload-section">
          <h4 class="section-title">
            <i class="el-icon-folder-opened"></i>
            选择版本文件
          </h4>
          <el-upload
            ref="upload"
            class="upload-demo"
            drag
            :action="uploadUrl"
            :headers="headers"
            :before-upload="handleBeforeUpload"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :on-progress="handleUploadProgress"
            :file-list="fileList"
            :on-change="handleFileChange"
            :limit="1"
            :on-exceed="handleExceed"
            :auto-upload="false"
            :show-file-list="false"
            accept=".zip,.rar,.tar,.gz,.7z,.jar"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将版本文件拖拽到此处，或<em>点击选择文件</em>
            </div>
          </el-upload>

          <!-- 已选择的文件信息 -->
          <div v-if="fileList.length > 0" class="selected-file">
            <div class="file-info">
              <i class="el-icon-document"></i>
              <span class="file-name">{{ fileList[0].name }}</span>
              <span class="file-size">({{ formatFileSize(fileList[0].size) }})</span>
              <el-button
                type="text"
                size="mini"
                @click="clearFiles"
                icon="el-icon-close"
                class="remove-file"
              >
                移除
              </el-button>
            </div>
          </div>

          <!-- 上传进度 -->
          <div v-if="uploadProgress > 0 && uploadProgress < 100" class="upload-progress">
            <el-progress
              :percentage="uploadProgress"
              :status="uploadStatus"
              :stroke-width="8"
            ></el-progress>
            <p class="progress-text">正在上传版本文件...</p>
          </div>

          <!-- 上传操作按钮 -->
          <div class="upload-actions">
            <el-button
              type="primary"
              size="medium"
              :loading="uploading"
              :disabled="!canUpload"
              @click="submitUpload"
              icon="el-icon-upload2"
            >
              {{ uploading ? '上传中...' : '开始上传' }}
            </el-button>
            <el-button
              size="medium"
              @click="clearFiles"
              :disabled="uploading"
              icon="el-icon-delete"
            >
              清空文件
            </el-button>
          </div>
        </div>

        <!-- 分隔线 -->
        <el-divider></el-divider>

        <!-- 系统重启区域 -->
        <div class="reboot-section">
          <h4 class="section-title">
            <i class="el-icon-refresh"></i>
            系统操作
          </h4>
          <el-alert
            title="重要提醒"
            type="warning"
            :closable="false"
            show-icon
          >
            <div slot="default">
              <p v-hasPermi="['system:upload:restart']">
                • <strong>重启服务</strong>：仅重启应用服务，约1-2分钟</p>
              <p>• <strong>重启系统</strong>：完全重启系统，约3-5分钟</p>
              <p>• 重启操作将中断所有用户连接，重启完成后正常访问</p>
              <p>• 版本更新需要重启系统生效，请确保已完成版本文件上传</p>
            </div>
          </el-alert>

          <div class="reboot-actions">
            <el-button
              type="warning"
              size="medium"
              :loading="restarting"
              @click="confirmRestart"
              icon="el-icon-refresh-left"
              v-hasPermi="['system:upload:restart']"
            >
              {{ restarting ? '重启中...' : '重启服务' }}
            </el-button>
            <el-button
              type="danger"
              size="medium"
              :loading="rebooting"
              @click="confirmReboot"
              icon="el-icon-refresh"
            >
              {{ rebooting ? '重启中...' : '重启系统' }}
            </el-button>
          </div>
        </div>
      </div>
    </el-card>


  </div>
</template>

<script>
import { uploadVersion, reboot, restart, getVersion } from "@/api/system/upload/upload";
import { getToken } from "@/utils/auth";

export default {
  name: "SystemUpload",

  mounted() {
      //获取版本号
      getVersion().then(res => {
        document.getElementById("versiontext").innerHTML=res.version;
      });
  },

  data() {
    return {
      // 上传相关
      fileList: [],
      uploading: false,
      uploadProgress: 0,
      uploadStatus: '',
      uploadUrl: process.env.VUE_APP_BASE_API + '/system/upload/v',
      headers: {
        Authorization: "Bearer " + getToken(),
      },

      // 重启相关
      rebooting: false,
      restarting: false
    };
  },
  computed: {
    // 是否可以上传
    canUpload() {
      const hasFiles = this.fileList && this.fileList.length > 0;
      console.log('计算canUpload:', hasFiles, !this.uploading, this.fileList);
      return hasFiles && !this.uploading;
    }
  },
  methods: {
    // 上传前验证
    handleBeforeUpload(file) {
      // 验证文件类型
      const allowedTypes = ['gz', '7z', 'jar'];
      const fileName = file.name.toLowerCase();
      const fileExt = fileName.substring(fileName.lastIndexOf('.') + 1);

      if (!allowedTypes.includes(fileExt)) {
        this.$modal.msgError('文件格式不支持，请上传 JAR 格式的文件！');
        return false;
      }

      // 验证文件大小 (500MB)
      const maxSize = 120 * 1024 * 1024;
      if (file.size > maxSize) {
        this.$modal.msgError('文件大小不能超过 120MB！');
        return false;
      }

      return true;
    },

    // 文件变化处理
    handleFileChange(file, fileList) {
      this.fileList = fileList;
      console.log('文件列表变化:', fileList.length, fileList);
    },

    // 文件数量超出限制
    handleExceed() {
      this.$modal.msgWarning('只能上传一个版本文件！');
    },

    // 上传进度
    handleUploadProgress(event, file, fileList) {
      this.uploadProgress = Math.round(event.percent);
      if (this.uploadProgress < 100) {
        this.uploadStatus = '';
      }
    },

    // 上传成功
    handleUploadSuccess(response, file, fileList) {
      this.uploading = false;
      this.uploadProgress = 100;
      this.uploadStatus = 'success';

      if (response.code === 200) {
        this.$modal.msgSuccess('版本文件上传成功！');
      } else {
        this.$modal.msgError(response.msg || '上传失败，请重试！');
        this.uploadStatus = 'exception';
      }

      // 3秒后重置进度条
      setTimeout(() => {
        this.uploadProgress = 0;
        this.uploadStatus = '';
      }, 3000);
    },

    // 上传失败
    handleUploadError(err, file, fileList) {
      this.uploading = false;
      this.uploadProgress = 0;
      this.uploadStatus = 'exception';
      this.$modal.msgError('上传失败，请检查网络连接后重试！');
    },

    // 开始上传
    submitUpload() {
      console.log('点击上传按钮，当前文件列表:', this.fileList.length, this.fileList);

      if (this.fileList.length === 0) {
        this.$modal.msgWarning('请先选择要上传的版本文件！');
        return;
      }

      this.uploading = true;
      this.uploadProgress = 0;
      this.$refs.upload.submit();
    },

    // 清空文件
    clearFiles() {
      this.$refs.upload.clearFiles();
      this.fileList = [];
      this.uploadProgress = 0;
      this.uploadStatus = '';
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    // 确认重启服务
    confirmRestart() {
      this.$modal.confirm('重启服务将中断所有用户连接，确定要继续吗？', '重启服务确认', {
        confirmButtonText: '确定重启',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true,
        message: `
          <div style="margin: 20px 0;">
            <p style="margin: 10px 0;"><i class="el-icon-warning" style="color: #E6A23C;"></i> 重启服务将执行以下操作：</p>
            <ul style="margin: 10px 0; padding-left: 20px;">
              <li>停止当前应用服务进程</li>
              <li>重新启动应用服务</li>
              <li>保持系统运行状态</li>
            </ul>
            <p style="margin: 10px 0; color: #E6A23C;"><strong>注意：重启过程大约需要 1-2 分钟，期间系统将无法访问！</strong></p>
          </div>
        `
      }).then(() => {
        this.handleRestart();
      }).catch(() => {
        // 用户取消了重启操作
      });
    },

    // 确认重启系统
    confirmReboot() {
      this.$modal.confirm('重启系统将中断所有用户连接，确定要继续吗？', '系统重启确认', {
        confirmButtonText: '确定重启',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true,
        message: `
          <div style="margin: 20px 0;">
            <p style="margin: 10px 0;"><i class="el-icon-warning" style="color: #E6A23C;"></i> 重启系统将执行以下操作：</p>
            <ul style="margin: 10px 0; padding-left: 20px;">
              <li>停止当前所有服务进程</li>
              <li>应用新上传的版本文件</li>
              <li>完全重启系统服务</li>
            </ul>
            <p style="margin: 10px 0; color: #F56C6C;"><strong>注意：重启过程大约需要 3-5 分钟，期间系统将无法访问！</strong></p>
          </div>
        `
      }).then(() => {
        this.handleReboot();
      }).catch(() => {
        // 用户取消了重启操作
      });
    },

    // 执行重启服务
    handleRestart() {
      this.restarting = true;

      restart().then(() => {
        this.$modal.msgSuccess('服务重启指令已发送，请稍等 1-2 分钟后刷新页面！');

        // 显示倒计时提示
        this.showRestartCountdown();
      }).catch(() => {
        this.restarting = false;
        this.$modal.msgError('服务重启失败，请联系系统管理员！');
      });
    },

    // 执行重启系统
    handleReboot() {
      this.rebooting = true;

      reboot().then(() => {
        this.$modal.msgSuccess('系统重启指令已发送，请稍等 3-5 分钟后刷新页面！');

        // 显示倒计时提示
        this.showRebootCountdown();
      }).catch(() => {
        this.rebooting = false;
        this.$modal.msgError('系统重启失败，请联系系统管理员！');
      });
    },

    // 显示服务重启倒计时
    showRestartCountdown() {
      let countdown = 90; // 1.5分钟倒计时
      const timer = setInterval(() => {
        countdown--;
        if (countdown <= 0) {
          clearInterval(timer);
          this.restarting = false;
          this.$modal.confirm('服务重启完成，是否刷新页面？', '重启完成', {
            confirmButtonText: '刷新页面',
            cancelButtonText: '稍后刷新',
            type: 'success'
          }).then(() => {
            window.location.reload();
          });
        }
      }, 1000);
    },

    // 显示系统重启倒计时
    showRebootCountdown() {
      let countdown = 180; // 3分钟倒计时
      const timer = setInterval(() => {
        countdown--;
        if (countdown <= 0) {
          clearInterval(timer);
          this.rebooting = false;
          this.$modal.confirm('系统重启完成，是否刷新页面？', '重启完成', {
            confirmButtonText: '刷新页面',
            cancelButtonText: '稍后刷新',
            type: 'success'
          }).then(() => {
            window.location.reload();
          });
        }
      }, 1000);
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
}



// 卡片通用样式
.el-card {
  margin-bottom: 24px;
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;

    .card-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      display: flex;
      align-items: center;

      i {
        margin-right: 8px;
        color: #409eff;
        font-size: 18px;
      }
    }
  }
}

// 更新卡片
.update-card {
  .update-content {
    padding: 20px 0;
  }

  .section-title {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    display: flex;
    align-items: center;

    i {
      margin-right: 8px;
      color: #409eff;
      font-size: 18px;
    }
  }

  .upload-section {
    margin-bottom: 20px;
  }

  .reboot-section {
    margin-top: 20px;
  }

  .upload-demo {
    margin-bottom: 24px;

    ::v-deep .el-upload {
      width: 100%;
    }

    ::v-deep .el-upload-dragger {
      width: 100%;
      height: 180px;
      border: 2px dashed #d9d9d9;
      border-radius: 8px;
      background-color: #fafafa;
      transition: all 0.3s ease;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      &:hover {
        border-color: #409eff;
        background-color: #f0f9ff;
      }

      .el-icon-upload {
        font-size: 42px;
        color: #c0c4cc;
        margin-bottom: 12px;
        transition: color 0.3s ease;
      }

      &:hover .el-icon-upload {
        color: #409eff;
      }
    }

    ::v-deep .el-upload__text {
      font-size: 15px;
      color: #606266;
      line-height: 1.5;

      em {
        color: #409eff;
        font-style: normal;
        font-weight: 500;
      }
    }
  }

  .selected-file {
    margin: 16px 0;
    padding: 12px 16px;
    background-color: #f0f9ff;
    border: 1px solid #b3d8ff;
    border-radius: 6px;

    .file-info {
      display: flex;
      align-items: center;
      gap: 8px;

      .el-icon-document {
        color: #409eff;
        font-size: 16px;
      }

      .file-name {
        flex: 1;
        color: #303133;
        font-size: 14px;
        font-weight: 500;
        word-break: break-all;
      }

      .file-size {
        color: #909399;
        font-size: 12px;
      }

      .remove-file {
        color: #f56c6c;
        padding: 0;
        margin-left: 8px;

        &:hover {
          color: #f78989;
        }
      }
    }
  }

  .upload-progress {
    margin: 20px 0;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;

    .progress-text {
      margin: 10px 0 0 0;
      text-align: center;
      color: #606266;
      font-size: 14px;
    }
  }

  .upload-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 20px;

    .el-button {
      padding: 10px 20px;
      font-size: 14px;
      border-radius: 6px;
      min-width: 110px;
    }
  }

  .reboot-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 20px;

    .el-button {
      padding: 12px 32px;
      font-size: 14px;
      border-radius: 6px;
      min-width: 140px;
    }
  }

  ::v-deep .el-alert {
    margin-bottom: 20px;
    border-radius: 6px;

    .el-alert__content {
      p {
        margin: 6px 0;
        font-size: 14px;
        line-height: 1.5;
      }
    }
  }

  ::v-deep .el-divider {
    margin: 30px 0;

    .el-divider__text {
      background-color: #fff;
      color: #909399;
      font-size: 14px;
    }
  }
}



// 响应式设计
@media (max-width: 768px) {
  .app-container {
    padding: 16px;
  }



  .update-card {
    .upload-actions,
    .reboot-actions {
      flex-direction: column;
      align-items: center;

      .el-button {
        width: 100%;
        max-width: 200px;
      }
    }

    .section-title {
      font-size: 15px;

      i {
        font-size: 16px;
      }
    }
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 12px;
  }
}

// 动画效果
.upload-demo {
  ::v-deep .el-upload-dragger {
    animation: fadeInUp 0.6s ease-out;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 加载状态样式
.el-button.is-loading {
  position: relative;
  pointer-events: none;

  &::before {
    content: '';
    position: absolute;
    left: -1px;
    top: -1px;
    right: -1px;
    bottom: -1px;
    border-radius: inherit;
    background-color: rgba(255, 255, 255, 0.35);
  }
}

// 进度条自定义样式
::v-deep .el-progress-bar__outer {
  border-radius: 4px;
  overflow: hidden;
}

::v-deep .el-progress-bar__inner {
  border-radius: 0;
  transition: width 0.3s ease;
}
</style>
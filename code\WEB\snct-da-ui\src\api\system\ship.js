import request from '@/utils/request'

// 查询船只列表
export function listShip(query) {
  return request({
    url: '/business/ship/list',
    method: 'get',
    params: query
  })
}

// 查询船只详细
export function getShip(shipId) {
  return request({
    url: '/business/ship/' + shipId,
    method: 'get'
  })
}

// 新增船只
export function addShip(data) {
  return request({
    url: '/business/ship',
    method: 'post',
    data: data
  })
}

// 修改船只
export function updateShip(data) {
  return request({
    url: '/business/ship',
    method: 'put',
    data: data
  })
}

// 删除船只
export function delShip(shipId) {
  return request({
    url: '/business/ship/' + shipId,
    method: 'delete'
  })
}

// 同步船只信息
export function syncShip() {
  return request({
    url: '/business/ship/sync',
    method: 'post'
  })
}

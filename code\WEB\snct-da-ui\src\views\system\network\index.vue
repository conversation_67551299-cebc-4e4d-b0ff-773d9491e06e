<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px">
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">

    </el-row>

    <el-table v-loading="loading" :data="networkList">
      <el-table-column label="网口名称" align="center" prop="name"/>
      <el-table-column label="配置模式" align="center" prop="mode"/>
      <el-table-column label="IP地址" align="center" prop="ip"/>
      <el-table-column label="子网掩码" align="center" prop="netmask"/>
      <el-table-column label="网关" align="center" prop="gateway"/>
      <el-table-column label="DNS1" align="center" prop="dns1"/>
      <el-table-column label="DNS2" align="center" prop="dns2"/>
      <!-- <el-table-column label="访问网段1" align="center" prop="visit1"/>
      <el-table-column label="访问网段2" align="center" prop="visit2"/>
      <el-table-column label="访问网段3" align="center" prop="visit3"/> -->

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:network:edit']"
            v-if="scope.row.ip !== currentIp"
          >修改
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="网口名称" prop="name">
          <el-input v-model="form.name" readonly/>
        </el-form-item>
        <el-form-item label="网口类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择">
            <el-option label="WAN" value="WAN"/>
            <el-option label="LAN" value="LAN"/>
          </el-select>
        </el-form-item>
        <el-form-item label="配置模式" prop="mode">
          <el-select v-model="form.mode" placeholder="请选择">
            <el-option
              v-for="m in modeOptions"
              :key="m"
              :label="m"
              :value="m"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="IP地址" prop="ip">
          <el-input v-model="form.ip" placeholder="请输入IP地址" :disabled="form.mode === 'DHCP'"/>
        </el-form-item>
        <el-form-item label="子网掩码" prop="netmask">
          <el-input v-model="form.netmask" placeholder="请输入子网掩码" :disabled="form.mode === 'DHCP'"/>
        </el-form-item>
        <el-form-item label="网关" prop="gateway">
          <el-input v-model="form.gateway" placeholder="请输入网关" :disabled="form.mode === 'DHCP'"/>
        </el-form-item>
        <el-form-item label="DNS1" prop="dns1" v-if="form.type === 'WAN'">
          <el-input v-model="form.dns1" placeholder="请输入DNS1"/>
        </el-form-item>
        <el-form-item label="DNS2" prop="dns2" v-if="form.type === 'WAN'">
          <el-input v-model="form.dns2" placeholder="请输入DNS2"/>
        </el-form-item>
        <el-form-item label="访问网段1" prop="visit1" v-if="form.type === 'LAN'">
          <el-input v-model="form.visit1" placeholder="访问网段1(格式：***********/24)"/>
        </el-form-item>
        <el-form-item label="访问网段2" prop="visit2" v-if="form.type === 'LAN'">
          <el-input v-model="form.visit2" placeholder="访问网段2(格式：***********/24)"/>
        </el-form-item>
        <el-form-item label="访问网段3" prop="visit3" v-if="form.type === 'LAN'">
          <el-input v-model="form.visit3" placeholder="访问网段3(格式：***********/24)"/>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="recov">重 置</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :visible.sync="rtsBeforeOpen"
      width="320px"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      center>
      <div class="progress-container">
        <div class="progress-icon">
          <i class="el-icon-loading" v-if="percentage2 < 100"></i>
          <i class="el-icon-check" v-else style="color: #67C23A;"></i>
        </div>
        <div class="progress-text">{{ rtsBeforeTitle }}</div>
        <div class="progress-bar">
          <el-progress
            :percentage="percentage2"
            :show-text="false"
            :stroke-width="6"
            color="#409EFF"/>
        </div>
        <div class="progress-percent">{{ percentage2 }}%</div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {getNetworkList, updateNetwork} from "@/api/system/network";

  export default {
    name: "Network",
    data() {
      return {
        // 遮罩层
        loading: true,
        // 总条数
        total: 0,
        // 表格数据
        networkList: [],
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        // 类型数据字典
        modeOptions: ['DHCP', 'STATIC'],
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10
        },
        // 表单参数
        form: {},
        // 表单校验
        rules: {},
        currentIp: '',
        rtsBeforeTitle: '',
        rtsBeforeOpen: false,
        percentage2: 0,
        interval:null,
        originalValue: undefined
      };
    },
    created() {
      this.currentIp = window.location.hostname;
      console.log(this.currentIp);
      this.getList();
    },
    methods: {
      /** 查询参数列表 */
      getList() {
        this.loading = true;
        getNetworkList().then(response => {
            this.networkList = response.data;
            this.total = this.networkList.length;
            for (let i = 0; i < this.networkList.length; i++) {
              if (this.networkList[i].name === 'enp1s0' || this.networkList[i].name === 'wan') {
                this.networkList[i].type = 'WAN';
              }
              if (this.networkList[i].name === 'enp2s0' || this.networkList[i].name === 'lan') {
                this.networkList[i].type = 'LAN';
              }
            }
            this.loading = false;
          }
        ).catch(() => {
          this.loading = false;
          this.$modal.msgError('获取网络配置列表失败');
        });
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
          name: undefined,
          type: 'WAN', // 默认选择WAN口
          mode: undefined,
          ip: undefined,
          netmask: undefined,
          gateway: undefined,
          dns1: undefined,
          dns2: undefined,
          visit1: undefined,
          visit2: undefined,
          visit3: undefined
        };
        this.resetForm("form");
        this.originalValue = undefined;
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset();
        this.originalValue = JSON.parse(JSON.stringify(row));
        this.form = JSON.parse(JSON.stringify(row));
        // 如果没有设置类型，默认为WAN
        if (!this.form.type) {
          this.form.type = 'WAN';
        }
        this.open = true;
        this.title = "修改参数";
        if (this.form.type === 'WAN') {
          this.modeOptions = ['DHCP', 'STATIC'];
        } else if (this.form.type === 'LAN') {
          this.modeOptions = ['STATIC'];
        }
      },
      recov() {
        this.form = JSON.parse(JSON.stringify(this.originalValue));
      },
      /** 提交按钮 */
      submitForm: function () {
        let _this = this;
        _this.percentage2 = 0;
        _this.rtsBeforeOpen = true;
        _this.rtsBeforeTitle = '正在修改网络配置...';
        clearInterval(_this.interval);
        _this.interval = null;
        _this.interval = setInterval(() => {
          // 进度条最多到90%，等待接口响应
          if (_this.percentage2 < 90) {
            _this.percentage2 += 3;
          }
        }, 500);
        updateNetwork(_this.form).then(response => {
          clearInterval(_this.interval);
          _this.percentage2 = 100;

          if (response.code === 200) {
            setTimeout(() => {
              _this.rtsBeforeOpen = false;
              _this.open = false;
              _this.reset();
              _this.$modal.msgSuccess("修改成功");
              _this.getList();
            }, 500);
          } else {
            _this.rtsBeforeOpen = false;
            _this.$modal.msgError(response.msg);
            _this.getList();
          }
        }).catch(function () {
          clearInterval(_this.interval);
          _this.rtsBeforeOpen = false;
          _this.percentage2 = 100;
          _this.getList();
        });
      }
    }
  };
</script>

<style scoped>
.progress-container {
  text-align: center;
  padding: 20px;
}

.progress-icon {
  font-size: 32px;
  color: #409EFF;
  margin-bottom: 16px;
}

.progress-icon .el-icon-loading {
  animation: rotating 2s linear infinite;
}

.progress-text {
  font-size: 16px;
  color: #303133;
  margin-bottom: 20px;
  font-weight: 500;
}

.progress-bar {
  margin-bottom: 12px;
}

.progress-percent {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
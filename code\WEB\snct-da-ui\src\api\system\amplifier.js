import request from '@/utils/request'

// 查询功放消息列表
export function listAmplifier(query) {
  return request({
    url: '/system/amplifier/list',
    method: 'get',
    params: query
  })
}

// 查询功放消息详细
export function getAmplifier(id) {
  return request({
    url: '/system/amplifier/' + id,
    method: 'get'
  })
}

// 新增功放消息
export function addAmplifier(data) {
  return request({
    url: '/system/amplifier',
    method: 'post',
    data: data
  })
}

// 修改功放消息
export function updateAmplifier(data) {
  return request({
    url: '/system/amplifier',
    method: 'put',
    data: data
  })
}

// 删除功放消息
export function delAmplifier(id) {
  return request({
    url: '/system/amplifier/' + id,
    method: 'delete'
  })
}

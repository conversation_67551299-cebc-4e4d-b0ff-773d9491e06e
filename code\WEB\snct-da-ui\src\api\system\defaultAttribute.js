import request from '@/utils/request'

// 查询设备默认属性列表
export function listDefaultAttribute(query) {
  return request({
    url: '/business/defaultAttribute/list',
    method: 'get',
    params: query
  })
}

// 查询设备默认属性详细
export function getDefaultAttribute(id) {
  return request({
    url: '/business/defaultAttribute/' + id,
    method: 'get'
  })
}

// 新增设备默认属性
export function addDefaultAttribute(data) {
  return request({
    url: '/business/defaultAttribute',
    method: 'post',
    data: data
  })
}

// 修改设备默认属性
export function updateDefaultAttribute(data) {
  return request({
    url: '/business/defaultAttribute',
    method: 'put',
    data: data
  })
}

// 删除设备默认属性
export function delDefaultAttribute(id) {
  return request({
    url: '/business/defaultAttribute/' + id,
    method: 'delete'
  })
}

<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="任务名称" prop="restartName">
        <el-input
          v-model="queryParams.restartName"
          placeholder="请输入任务名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:restart:add']"
        >新增</el-button>
      </el-col>
      <!--
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:restart:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:restart:remove']"
        >删除</el-button>
      </el-col>

      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:restart:export']"
        >导出</el-button>
      </el-col>
      -->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="restartList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!--
      <el-table-column label="任务ID" align="center" prop="restartId" />
      -->
      <el-table-column label="任务名称" align="center" prop="restartName" />
      <el-table-column label="任务类型" align="center" prop="restartType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.restart_type" :value="scope.row.restartType"/>
        </template>
      </el-table-column>
      <el-table-column label="重启时间" align="center" prop="restartTime" />
      <el-table-column label="重启日期" align="center" prop="today">
        <template slot-scope="scope">
          <span v-if="scope.row.restartType != 1">{{ formatWeekDays(scope.row.today) }}</span>
          <span v-else>每日</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:restart:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:restart:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改任务对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">

        <el-form-item label="任务名称" prop="restartName">
          <el-input v-model="form.restartName" placeholder="请输入任务名称" />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="任务类型" prop="restartType">
              <el-select
                id="srestartType"
                v-model="form.restartType"
                @change="onchange"
                placeholder="请选择任务类型"
                style="width: 100%;"
              >
                <el-option
                  v-for="dict in dict.type.restart_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item id="today" label="重启日期" prop="today" v-show="showWeekSelect">
              <el-select
                id="stoday"
                v-model="form.today"
                placeholder="请选择重启日期（可多选）"
                style="width: 100%;"
                clearable
                multiple
                collapse-tags
              >
                <el-option
                  v-for="week in weekOptions"
                  :key="week.value"
                  :label="week.label"
                  :value="week.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="重启时间" prop="restartTime">
          <el-time-picker
            v-model="form.restartTime"
            format="HH:mm:ss"
            value-format="HH:mm:ss"
            placeholder="请选择重启时间"
            style="width: 180px;"
          />
        </el-form-item>

        <el-form-item label="任务状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>


        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listrestart, getrestart, delrestart, addrestart, updaterestart } from "@/api/system/restart";

export default {
  name: "restart",
  dicts: ["restart_type", "sys_normal_disable","week_type"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 任务表格数据
      restartList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示周选择器
      showWeekSelect: false,
      // 周日期选项
      weekOptions: [
        { value: 1, label: '星期一' },
        { value: 2, label: '星期二' },
        { value: 3, label: '星期三' },
        { value: 4, label: '星期四' },
        { value: 5, label: '星期五' },
        { value: 6, label: '星期六' },
        { value: 7, label: '星期日' }
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        restartCode: null,
        restartName: null,
        restartSort: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        restartCode: [
          { required: true, message: "任务类型不能为空", trigger: "blur" }
        ],
        today: [
          {
            validator: (rule, value, callback) => {
              if (this.showWeekSelect && (!value || value.length === 0)) {
                callback(new Error('请选择重启日期'));
              } else {
                callback();
              }
            },
            trigger: 'change'
          }
        ],
        restartTime: [
          { required: true, message: "重启时间不能为空", trigger: "blur" }
        ],
        restartType: [
          { required: true, message: "重启时间不能为空", trigger: "blur" }
        ],
        restartName: [
          { required: true, message: "任务名称不能为空", trigger: "blur" }
        ],
        restartSort: [
          { required: true, message: "参数不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  computed: {
    // 周日期映射
    weekDayMap() {
      const map = {};
      this.weekOptions.forEach(item => {
        map[item.value] = item.label;
      });
      return map;
    }
  },
  methods: {
    // 格式化显示重启日期
    formatWeekDays(todayValue) {
      if (!todayValue) return '-';

      // 如果是字符串，按逗号分割
      let days = [];
      if (typeof todayValue === 'string') {
        days = todayValue.split(',').map(item => parseInt(item.trim()));
      } else if (Array.isArray(todayValue)) {
        days = todayValue;
      } else {
        days = [todayValue];
      }

      // 过滤无效值并排序
      days = days.filter(day => day >= 1 && day <= 7).sort((a, b) => a - b);

      if (days.length === 0) return '-';

      const weekMap = {
        1: '一', 2: '二', 3: '三', 4: '四',
        5: '五', 6: '六', 7: '日'
      };

      if (days.length === 1) {
        return `星期${weekMap[days[0]]}`;
      } else {
        const firstDay = `星期${weekMap[days[0]]}`;
        const otherDays = days.slice(1).map(day => weekMap[day]);
        return `${firstDay}、${otherDays.join('、')}`;
      }
    },
    /** 查询任务列表 */
    getList() {
      this.loading = true;
      listrestart(this.queryParams).then(response => {
        this.restartList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 选择类型
    onchange(value) {
      this.showWeekSelect = value != 1;
      if (value == 1) {
        this.form.today = [];
      }
    },
    // 表单重置
    reset() {
      this.form = {
        restartId: null,
        restartCode: null,
        restartTime: null,
        restartType: null,
        restartName: null,
        restartSort: null,
        status: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null,
        today: []
      };
      this.showWeekSelect = false;
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.restartId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加任务";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const restartId = row.restartId || this.ids
      getrestart(restartId).then(response => {
        this.form = response.data;
        // 根据任务类型设置是否显示周选择器
        this.showWeekSelect = this.form.restartType != 1;

        // 处理today字段的回显，如果是字符串则转换为数组
        if (this.form.today && typeof this.form.today === 'string') {
          this.form.today = this.form.today.split(',').map(item => parseInt(item));
        } else if (!Array.isArray(this.form.today)) {
          this.form.today = [];
        }

        this.open = true;
        this.title = "修改任务";
      });
    },
    /** 提交按钮 */
    submitForm() {
      // 处理提交数据
      const submitData = { ...this.form };

      // 如果是每日任务类型，设置默认today值
      if (this.form.restartType == 1 || !this.showWeekSelect) {
        submitData.today = 1;
      } else {
        // 如果是周任务且选择了多个日期，将数组转换为逗号分隔的字符串
        if (Array.isArray(this.form.today) && this.form.today.length > 0) {
          submitData.today = this.form.today.join(',');
        } else if (!this.form.today || this.form.today.length === 0) {
          this.$modal.msgError("请选择重启日期");
          return;
        }
      }

      this.$refs["form"].validate(valid => {
        if (valid) {
          if (submitData.restartId != null) {
            updaterestart(submitData).then(() => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addrestart(submitData).then(() => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const restartIds = row.restartId || this.ids;
      this.$modal.confirm('是否确认删除数据项？').then(function() {
        return delrestart(restartIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/restart/export', {
        ...this.queryParams
      }, `restart_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
